// 字幕换行功能测试文件
// 用于验证动态换行机制的效果

const testCases = [
  {
    name: "中文长字幕测试",
    language: "zh",
    text: "这是一个非常长的中文字幕文本，用来测试我们的智能换行算法是否能够正确地处理中文文本的换行问题，确保字幕在视频画面中完整可见且美观。",
    videoResolution: { width: 1920, height: 1080 },
    styleConfig: { fontSize: 42, marginL: 96, marginR: 96 }
  },
  {
    name: "英文长字幕测试",
    language: "en", 
    text: "This is a very long English subtitle text that is used to test whether our intelligent line wrapping algorithm can correctly handle the line breaking issues of English text and ensure that subtitles are completely visible and aesthetically pleasing in the video frame.",
    videoResolution: { width: 1920, height: 1080 },
    styleConfig: { fontSize: 40, marginL: 96, marginR: 96 }
  },
  {
    name: "竖屏视频中文测试",
    language: "zh",
    text: "竖屏视频中的字幕需要特别处理，因为可用宽度较小，需要更频繁的换行来确保文本的可读性。",
    videoResolution: { width: 720, height: 1280 },
    styleConfig: { fontSize: 32, marginL: 58, marginR: 58 }
  },
  {
    name: "4K视频英文测试",
    language: "en",
    text: "Ultra-high definition 4K videos require larger font sizes and more sophisticated text wrapping algorithms to maintain readability across different screen sizes and viewing distances.",
    videoResolution: { width: 3840, height: 2160 },
    styleConfig: { fontSize: 70, marginL: 192, marginR: 192 }
  },
  {
    name: "日文混合文本测试",
    language: "ja",
    text: "これは日本語のテストです。This text contains both Japanese characters and English words to test mixed-language wrapping capabilities.",
    videoResolution: { width: 1920, height: 1080 },
    styleConfig: { fontSize: 42, marginL: 96, marginR: 96 }
  }
];

// 模拟字符宽度估算函数
function estimateCharacterWidth(targetLanguage, fontSize) {
  const characterWidthFactors = {
    'zh': 1.0, 'zh-cn': 1.0, 'zh-tw': 1.0, 'ja': 1.0, 'ko': 1.0,
    'en': 0.6, 'fr': 0.6, 'de': 0.6, 'es': 0.6, 'it': 0.6, 'pt': 0.6,
    'ru': 0.7, 'ar': 0.8, 'hi': 0.8, 'th': 0.8, 'vi': 0.6, 'default': 0.7
  };
  
  const widthFactor = characterWidthFactors[targetLanguage] || characterWidthFactors.default;
  return Math.ceil(fontSize * widthFactor);
}

// 模拟可用宽度计算函数
function calculateAvailableTextWidth(videoResolution, styleConfig) {
  const { width } = videoResolution;
  const { marginL = 0, marginR = 0 } = styleConfig;
  const availableWidth = width - marginL - marginR;
  return Math.floor(availableWidth * 0.9);
}

// 模拟文本显示长度计算函数
function getTextDisplayLength(text, targetLanguage) {
  if (!text) return 0;
  
  const cjkLanguages = ['zh', 'zh-cn', 'zh-tw', 'ja', 'ko'];
  const isCJK = cjkLanguages.includes(targetLanguage);
  
  if (isCJK) {
    let length = 0;
    for (const char of text) {
      if (/[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/.test(char)) {
        length += 1; // CJK字符
      } else {
        length += 0.6; // 其他字符
      }
    }
    return length;
  } else {
    return text.length * 0.6;
  }
}

// 运行测试
function runTests() {
  console.log("🧪 字幕换行功能测试开始\n");
  
  testCases.forEach((testCase, index) => {
    console.log(`📝 测试案例 ${index + 1}: ${testCase.name}`);
    console.log(`   语言: ${testCase.language}`);
    console.log(`   视频分辨率: ${testCase.videoResolution.width}x${testCase.videoResolution.height}`);
    console.log(`   字体大小: ${testCase.styleConfig.fontSize}px`);
    console.log(`   原始文本: "${testCase.text}"`);
    
    // 计算技术参数
    const availableWidth = calculateAvailableTextWidth(testCase.videoResolution, testCase.styleConfig);
    const avgCharWidth = estimateCharacterWidth(testCase.language, testCase.styleConfig.fontSize);
    const maxCharsPerLine = Math.floor(availableWidth / avgCharWidth);
    const originalLength = getTextDisplayLength(testCase.text, testCase.language);
    
    console.log(`   可用宽度: ${availableWidth}px`);
    console.log(`   平均字符宽度: ${avgCharWidth}px`);
    console.log(`   每行最大字符数: ${maxCharsPerLine}`);
    console.log(`   原始文本长度: ${Math.round(originalLength)}字符`);
    
    // 判断是否需要换行
    const needsWrapping = originalLength > maxCharsPerLine;
    const estimatedLines = Math.ceil(originalLength / maxCharsPerLine);
    
    console.log(`   是否需要换行: ${needsWrapping ? '是' : '否'}`);
    if (needsWrapping) {
      console.log(`   预估行数: ${estimatedLines}`);
    }
    
    console.log("   " + "=".repeat(60) + "\n");
  });
  
  console.log("✅ 测试完成");
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  runTests();
}

module.exports = {
  testCases,
  runTests,
  estimateCharacterWidth,
  calculateAvailableTextWidth,
  getTextDisplayLength
};
